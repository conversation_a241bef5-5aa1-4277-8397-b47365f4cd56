<template>
  <!-- 页面级智能体布局容器 -->
  <div class="page-ai-agent-layout" :class="{ 'ai-agent-active': showAiAgent }">
    <!-- 主要页面区域 -->
    <div class="main-page-area" :class="{ 'with-ai-agent': showAiAgent }">
      <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
        <template #header>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
              <text-collapse :text="`【${basicWorkOrderData.professionalType || ''}故障工单】${headInfo.title
                }`" :max-lines="2"></text-collapse>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
              <el-button-group>
                <el-button type="button" @click="onHeadHandleClick('jcxx')">基础信息</el-button>
                <el-button type="button" @click="onHeadHandleClick('gjxq')">告警详情</el-button>
                <el-button type="button" v-if="ppResult" @click="onHeadHandleClick('glzd')">关联诊断</el-button>
                <el-button type="button" @click="onHeadHandleClick('fkdxq')">反馈单详情</el-button>
                <el-dropdown @command="onHeadHandleClick" class="el-button more-dropdown" size="medium">
                  <el-button type="button">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="(item, i) in headMoreDrops" :key="i" :command="item.command">
                      {{ item.title }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-button-group>
              <br />
              <!-- 按钮动态展示 -->
              <el-button size="mini" type="primary" v-for="(item, i) in processButtonArr" class="btnleft__group" :key="i"
                @click="buttonClick(item.value, item.desc)" v-loading.fullscreen.lock="processFullscreenLoading">{{
                  item.value }}</el-button>
            </el-col>
          </el-row>
          <el-divider direction="horizontal" content-position="left" class="divider"></el-divider>
          <div class="head-info2">
            <el-row :gutter="20" tag="p">
              <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p" class="head-sheetId">
                工单编号: {{ headInfo.sheetId }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
                <el-row :gutter="20" type="flex">
                  <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down">
                    <div>当前处理人</div>
                    <div class="text-truncate" @click="currentProcessor" style="
                        cursor: pointer;
                        text-decoration: underline;
                        color: #b50b14;
                        user-select: unset;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        font-size: 18px;
                        line-height: 28px;
                      " :title="headInfo.currentHandler">
                      {{ headInfo.currentHandler }}
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="4" tag="p" class="head-up-down">
                    <div>工单状态</div>
                    <div>
                      {{ headInfo.sheetStatus }}
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down"
                    v-if="headInfo.sheetStatus == '待受理'">
                    <div>剩余受理时间</div>
                    <div class="text-primary" :title="setAcceptTimeLimit">
                      {{ setAcceptTimeLimit }}
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down" v-if="
                    [
                      '处理中',
                      '待定性',
                      '待确认',
                      '待定性审核',
                      '挂起',
                      '上传故障报告',
                      '故障报告审核',
                    ].includes(headInfo.sheetStatus)
                  ">
                    <div>剩余处理时间</div>
                    <div class="text-primary" :title="setEstimatedProTimeLimit">
                      {{ setEstimatedProTimeLimit }}
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="6" tag="p" class="head-up-down">
                    <div>工单总耗时</div>
                    <div class="text-primary" :title="headInfo.totalWorkingTime">
                      {{ headInfo.totalWorkingTime }}
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row :gutter="20" tag="p">
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                建单人: {{ headInfo.buildSingleMan }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                建单部门: {{ headInfo.buildSingleDept }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                建单时间: {{ headInfo.buildSingleTime }}
              </el-col>
            </el-row>
            <el-row :gutter="20" tag="p">
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                工单来源: {{ headInfo.sourceInfo }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                发生时间: {{ headInfo.occurrenceTime }}
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
                紧急程度: {{ headInfo.emergencyDegree }}
              </el-col>
            </el-row>
          </div>
        </template>

        <!-- 内容区域 -->
        <base-info ref="jcxx" v-if="basicWorkOrderData" :basicWorkOrderData="basicWorkOrderData" :woId="common.woId"
          :workItemId="common.workItemId" />
        <alarm-detail ref="gjxq" v-if="common.woId && headInfo.occurrenceTime" :common="common"
          :occurrenceTime="headInfo.occurrenceTime" :isShowClearAlarmApply="isShowClearAlarmApply"
          :isShowManualConfirm="isShowManualConfirm" @alarmClearCallBack="alarmClearCallBack" />
        <relation-diagnosis ref="glzd" v-if="common.woId && ppResult" :woId="common.woId" :ppResult="ppResult"
          :analysisStatus="analysisStatus" />
        <feedback-sheet ref="fkdxq" v-if="showFkdxq && common.woId" :isShowAudit="isShowQualitativeReviewButton"
          :isShowQualitative="isShowQualitative" :common="common" :woId="common.woId" :qualitativeType="qualitativeType"
          @qualitativeReviewSubmit="qualitativeReviewSubmit" @qualitativeSubmit="qualitativeSubmit" />
        <report-records ref="gzbgscjl" v-if="reportRecordsShow" :isShowReportReview="isShowReportReview"
          :reportRecordsData="reportRecordsData" :woId="common.woId" @closeDialogReportAudit="closeDialogReportAudit" />
        <deal-details ref="clxq" v-if="showClxq" :woId="common.woId" :common="common" />
        <process-log ref="lcrz" v-if="showLcrz" :woId="common.woId" />
        <flow-chart ref="lct" v-if="showLct" :common="common" />
      </head-fixed-layout>
    </div>

    <!-- 页面级智能体区域 -->
    <div class="page-ai-agent-area" v-if="showAiAgent">
      <div class="ai-agent-header">
        <span class="ai-agent-title">智能体助手</span>
        <el-button
          type="text"
          icon="el-icon-close"
          @click="toggleAiAgent"
          class="ai-agent-close-btn">
        </el-button>
      </div>
      <div class="ai-agent-content" v-loading="aiAgentLoading">
        <!-- 有实际URL时显示iframe -->
        <iframe
          v-if="aiAgentUrl && !aiAgentError"
          ref="aiAgentIframe"
          :src="aiAgentUrl"
          frameborder="0"
          width="100%"
          height="100%"
          @load="onAiAgentLoad"
          @error="onAiAgentError">
        </iframe>
        <!-- 临时占位内容 -->
        <div v-else-if="!aiAgentUrl && !aiAgentError && !aiAgentLoading" class="ai-agent-placeholder">
          <div class="placeholder-content">
            <i class="el-icon-cpu"></i>
            <h3>智能体助手</h3>
            <p>智能体功能正在开发中...</p>
            <p>此区域将用于显示智能体内容</p>
            <div class="placeholder-features">
              <div class="feature-item">
                <i class="el-icon-chat-dot-round"></i>
                <span>智能问答</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-data-analysis"></i>
                <span>故障分析</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-guide"></i>
                <span>处理建议</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 错误状态 -->
        <div v-if="aiAgentError" class="ai-agent-error">
          <i class="el-icon-warning"></i>
          <p>智能体加载失败</p>
          <el-button type="primary" size="small" @click="retryLoadAiAgent">重试</el-button>
        </div>
      </div>
    </div>
  </div>

    <!-- 智能体悬浮控制按钮 -->
    <div
      v-if="isAiAgentAvailable"
      class="ai-agent-float-btn"
      :class="{ 'active': showAiAgent }"
      @click="toggleAiAgent"
      :title="showAiAgent ? '隐藏智能体助手' : '显示智能体助手'">
      <i class="el-icon-cpu"></i>
      <span class="btn-text">{{ showAiAgent ? '隐藏助手' : '智能助手' }}</span>
      <div class="btn-indicator" :class="{ 'show': showAiAgent, 'hide': !showAiAgent }"></div>
    </div>
    <el-dialog title="返单" :visible.sync="dialogBackSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogBackSingleClose" :fullscreen="false" width="83%" top="5vh">
      <span slot="title">
        <span style="line-height: 24px; font-size: 18px; color: #333">返单</span>
        <el-button v-if="ruleButtonVisible" type="primary" style="float: right; margin-right: 35px"
          @click="changeRuleVisible">返单审核规则</el-button>
      </span>
      <back-single ref="backSingleForm" :basicWorkOrderData="basicWorkOrderData" :common="common" :timing="timing"
        @closeBackSingleDialog="dialogBackSingleSubmitClose"
        @professionalTypeChange="professionalTypeChange"></back-single>
    </el-dialog>
    <el-dialog width="620px" title="返单审核规则" :visible.sync="ruleVisible" :close-on-click-modal="false" append-to-body>
      <p style="
          font-size: 16px;
          line-height: 130%;
          margin-top: 0;
          margin-bottom: 0;
        ">
        返单界面标记“*”符号字段必须按实际情况填写，审核时重点关注内容如下，填写信息包含即可，返单页面无对应字段的请自行选择字段填写：<br />
        (1)故障排查对象（具体系统、网元、设备）<br />
        (2)排查结果<br />
        (3)故障影响范围（据实反馈故障影响业务情况）<br />
        (4)故障处理情况<br />
        (5)故障原因分析<br />
        (6)反馈人信息(联系方式)。<br />
      </p>
    </el-dialog>
    <el-dialog title="追单" :visible.sync="dialogAfterSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAfterSingleClose" width="600px">
      <after-single :common="common" @closeAfterSingleDialog="dialogAfterSingleSubmitClose"></after-single>
    </el-dialog>
    <el-dialog title="阶段反馈" :visible.sync="dialogStageFeedbackVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogStageFeedbackClose" width="450px">
      <stage-feedback :common="common" @stageBackDialogClose="stageBackDialogCommitClose"></stage-feedback>
    </el-dialog>
    <el-dialog title="撤单" :visible.sync="dialogRevokeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogRevokeClose" width="480px">
      <el-form ref="revokeForm" :model="revokeForm" :rules="revokeFormRules" label-width="90px">
        <el-form-item label="审核意见:" prop="auditOpinion">
          <el-input type="textarea" :rows="2" placeholder="请填写审核意见" v-model="revokeForm.auditOpinion"
            style="width: 300px" show-word-limit maxlength="1000">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleRevokeSubmit('revokeForm')"
          v-loading.fullscreen.lock="revokeSubmitLoading">提 交</el-button>
        <el-button @click="onResetRevoke">重 置</el-button>
      </div>
    </el-dialog>
    <el-dialog title="挂起" :visible.sync="dialogHangUpVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogHangUpClose" width="540px">
      <hang-up :common="common" :opType="1" @closeDialogHangUp="dialogHangUpSubmitClose"></hang-up>
    </el-dialog>
    <el-dialog title="解挂" :visible.sync="dialogSolutionToHangVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangClose" width="540px">
      <hang-up :common="common" :opType="2" @closeDialogHangUp="dialogSolutionToHangSubmitClose"></hang-up>
    </el-dialog>
    <el-dialog title="挂起审核" :visible.sync="dialogPendingReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogPendingReviewClose" width="450px">
      <audit :common="common" :opContent="opContent" @closeDialogPendingReview="dialogPendingReviewSubmitClose"></audit>
    </el-dialog>
    <el-dialog title="解挂审核" :visible.sync="dialogSolutionToHangAuditVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangAuditClose" width="450px">
      <audit :common="common" :opContent="opContent"
        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose">
      </audit>
    </el-dialog>
    <el-dialog title="异常终止" :visible.sync="dialogAbendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAbendClose" width="450px">
      <abend :common="common" @closeDialogAbend="dialogAbendSubmitClose"></abend>
    </el-dialog>
    <el-dialog title="异常终止审核" :visible.sync="dialogAbendAuditVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAbendAuditClose" width="450px">
      <abend-audit :common="common" @closeDialogAbendAudit="dialogAbendAuditSubmitClose"></abend-audit>
    </el-dialog>
    <el-dialog title="转派" :visible.sync="dialogTurnToSendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToSendClose" width="480px">
      <turn-to-send @closeDialogTurnToSend="dialogTurnToSendSubmitClose" :common="common" :type="type"
        actionName="转派"></turn-to-send>
    </el-dialog>
    <el-dialog title="转办" :visible.sync="dialogTurnToVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToClose" width="480px">
      <turn-to @closeDialogTurnToSend="dialogTurnToSubmitClose" :common="common" :type="type" actionName="转办"></turn-to>
    </el-dialog>
    <el-dialog title="故障报告转派" :visible.sync="dialogReportTransferVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogTurnToSendClose" width="480px">
      <report-transfer @closeDialogTurnToSend="dialogReportTransferClose" :common="common" :type="type"
        actionName="故障报告转派"></report-transfer>
    </el-dialog>
    <el-dialog title="重新转派" :visible.sync="dialogAgainTurnToSendVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAgainTurnToSendClose" width="480px">
      <turn-to-send @closeDialogTurnToSend="dialogAgainTurnToSendSubmitClose" :common="common" :type="type"
        actionName="重新转派"></turn-to-send>
    </el-dialog>
    <one-key-ivr :dialogOneKeyIvrNotice.sync="dialogOneKeyIvrNotice" :common="common"
      :sheetId="headInfo.sheetId && headInfo.sheetId"
      :sheetCreateTime="headInfo.buildSingleTime && headInfo.buildSingleTime" />
    <el-dialog title="定性审核" :visible.sync="dialogQualitativeReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeReviewClose" width="83%" top="5vh">
      <qualitative-review ref="qualitativeReview" :common="common" :workItemId="common.workItemId" v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"></qualitative-review>
    </el-dialog>
    <el-dialog title="上传故障报告" :visible.sync="dialogReportUpVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogSolutionToHangClose" width="600px">
      <report-up :common="common" :sheetCreateTime="headInfo.buildSingleTime"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"></report-up>
    </el-dialog>
    <el-dialog title="定性" :visible.sync="dialogQualitativeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" :common="common" @close="qualitativeClose" width="83%" top="5vh">
      <qualitative ref="qualitative" :common="common" :workItemId="common.workItemId"
        @qualitativeSubmit="qualitativeSubmit"></qualitative>
    </el-dialog>
    <el-dialog title="处理人" :visible.sync="dialogCurrentProcessorVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="currentProcessorClose" :fullscreen="false" width="60%" top="5vh">
      <current-processor :common="common" :persons="basicWorkOrderData.operatePersonId">
      </current-processor>
    </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import ProcessLog from "../workOrder/workOrderWaitDetail/components/ProcessLog.vue";
import FlowChart from "../workOrder/workOrderWaitDetail/components/FlowChart.vue";
import BackSingle from "./components/BackSingle.vue";
import StageFeedback from "../workOrder/workOrderWaitDetail/components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
import Abend from "./components/Abend.vue";
import AfterSingle from "./components/AfterSingle.vue";
import AbendAudit from "./components/AbendAudit.vue";
import TurnToSend from "./components/TurnToSend.vue";
import TurnTo from "./components/TurnTo.vue";
import oneKeyIvr from "../commCloud/components/CommCloudoneKeyIVR.vue";
import QualitativeReview from "./components/QualitativeReview.vue";
import ReportUp from "./components/ReportUp.vue";
import Qualitative from "./components/Qualitative.vue";
import ReportRecords from "./components/ReportRecords.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import ReportTransfer from "./components/ReportTransfer.vue";
import CurrentProcessor from "../workOrder/components/CurrentProcessor.vue";
import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  // apiItCloudAccept,
  apiRevoke,
  apiHaveRead,
  apiGetReportUpLog,
  apiRotationSubmit
} from "./api/CommonApi";
import { checkAiAgentExists } from "@/plugin/backbone/modules/api/generalApi";
import { mixin } from "../../../../mixins";
export default {
  name: "WorkOrderWaitDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    BackSingle,
    RelationDiagnosis,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AfterSingle,
    AbendAudit,
    TurnToSend,
    oneKeyIvr,
    Qualitative,
    QualitativeReview,
    ReportUp,
    ReportRecords,
    TextCollapse,
    ReportTransfer,
    CurrentProcessor,
    TurnTo
  },
  mixins: [mixin],
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
        estimatedProTimeLimit: "", //剩余处理时间隔
      },
      headMoreDrops: [
        { command: "gzbgscjl", title: "故障报告上传记录" },
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: "",
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        professionalTypeName: null, //专业类型名称
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        busName: null, //业务名称
        auditResult: null,
        isUploadReport: null, //是否上传故障报告
        remainAcceptTime: null, //剩余处理时间
        alarmClearTime: null,
        isObject: null, //追单 0：追信息 1：追人
        mecNodeName: "",
        emergencyLevel: "",
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      ppResult: null,
      analysisStatus: 0,
      processFullscreenLoading: false,
      isShowClearAlarmApply: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,
      //返单
      dialogBackSingleVisible: false, //一干
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      revokeFormRules: {
        auditOpinion: [
          {
            required: true,
            message: "请填写审核意见",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "revokeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      //当前处理人
      dialogCurrentProcessorVisible: false,
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //追单
      dialogAfterSingleVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //转办
      dialogTurnToVisible: false,
      //故障报告转派
      dialogReportTransferVisible: false,
      //类型
      type: "single",
      //重新转派
      dialogAgainTurnToSendVisible: false,
      //一键IVR
      dialogOneKeyIvrNotice: false,
      //故障报告上传
      dialogReportUpVisible: false,
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      isShowManualConfirm: false,
      dialogQualitativeReviewVisible: false, //定性审核弹出框
      isShowQualitativeReviewButton: false,
      isShowQualitative: false,
      isShowReportReview: false, //故障报告审核按钮
      dialogQualitativeVisible: false, //定性弹出框
      qualitativeType: "",
      showDxsh: false,
      isBackSingle: false,
      ruleVisible: false,
      ruleButtonVisible: false,
      reportRecordsData: [],
      reportRecordsShow: false,
      // 智能体相关数据
      isAiAgentAvailable: false, // 智能体功能是否可用
      showAiAgent: false, // 是否显示智能体区域
      aiAgentLoading: false, // 智能体加载状态
      aiAgentError: false, // 智能体加载错误状态
      aiAgentUrl: '', // 智能体页面URL
      aiAgentConfig: null, // 智能体配置信息
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      return this.setTime(time);
    },
    setEstimatedProTimeLimit() {
      let time = this.headInfo.estimatedProTimeLimit;
      // if (this.headInfo.sheetStatus == "待定性审核") {
      //   time = "-";
      // }

      return this.setTime(time);
    },
  },
  created() {
    this.route = this.$route;
  },
  mounted() {
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.fromPage = this.$route.query.fromPage;
    // if (this.fromPage == "已阅工单") {
    //   this.getWorkOrderInfo();
    // } else if (this.fromPage == "待阅工单") {
    //   this.getShowButton().then(() => {
    //     this.getWorkOrderInfo();
    //   });
    //   // this.processButtonArr = [{ key: "links", value: "已阅" }];
    //   // this.getWorkOrderInfo();
    // } else {
    this.getShowButton().then(() => {
      this.getWorkOrderInfo();
    });
    this.getReportRecords();
    // 检查智能体功能
    this.checkAiAgentAvailability();
    // }
  },
  methods: {
    setTime(time) {
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        const absTime = Math.abs(time);
        let format = "已超时";
        if (absTime < 60) {
          format += `${absTime}分`;
        } else {
          const minFormat = absTime % 60 == 0 ? "" : `${absTime % 60}分`;
          format += `${Math.floor(absTime / 60)}小时${minFormat}`;
        }
        return format;
      } else if (time > 0) {
        if (time < 60) {
          return `${time}分`;
        } else {
          const minFormat = time % 60 == 0 ? "" : `${time % 60}分`;
          return `${Math.floor(time / 60)}小时${minFormat}`;
        }
      }
    },
    setDialogOneKeyIvrNotice(val) {
      this.dialogOneKeyIvrNotice = val;
    },
    changeRuleVisible() {
      this.ruleVisible = true;
    },
    professionalTypeChange(value) {
      if (
        value == "1" ||
        value == "5" ||
        value == "6" ||
        value == "12" ||
        value == "13" ||
        value == "19" ||
        value == "20" ||
        value == "21" ||
        value == "25" ||
        value == "28"
      ) {
        this.ruleButtonVisible = true;
      } else {
        this.ruleButtonVisible = false;
      }
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          fromPage: this.fromPage,
        };
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data?.user ?? [];
              // 临时添加
              // self.processButtonArr.push({
              //   "key": "links",
              //   "value": "转办",
              //   "desc": "edit"
              // });
              let alarmButton = res?.data?.admin ?? [];
              let qualitativeReviewButton = res?.data?.admin ?? [];
              let reportReviewButton = res?.data?.admin ?? [];
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性"
              ) {
                this.isShowQualitative = true;
                this.qualitativeType = "定性";
              } else {
                this.isShowQualitative = false;
              }
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性审核"
              ) {
                this.isShowQualitativeReviewButton = true;
                this.qualitativeType = "定性审核";
              } else {
                this.isShowQualitativeReviewButton = false;
              }
              if (
                reportReviewButton.length > 0 &&
                reportReviewButton[0].value == "故障报告审核"
              ) {
                this.isShowReportReview = true;
              } else {
                this.isShowReportReview = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "清除告警申请")
              ) {
                this.isShowClearAlarmApply = true;
              } else {
                this.isShowClearAlarmApply = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "人工确认清除告警")
              ) {
                this.isShowManualConfirm = true;
              } else {
                this.isShowManualConfirm = false;
              }
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      let formData = new FormData();
      formData.append("woId", this.common.woId);
      formData.append(
        "processInstId",
        this.common.processInstId ? this.common.processInstId : ""
      );
      formData.append(
        "workItemId",
        this.common.workItemId ? this.common.workItemId : ""
      );
      apiGetWorkOrderInfo(formData)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.rows?.[0] ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            self.headInfo.currentHandler =
              self.basicWorkOrderData.operatePerson; // 当前处理人
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.estimatedProTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatus;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createType;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevel;
            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.senderName == self.userInfo.realName
                ? 1
                : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;
            // 添加受理时限
            self.common.acceptTimeLimit = self.basicWorkOrderData.acceptTimeLimit;
            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            self.common.professionalType =
              self.basicWorkOrderData.professionalTypeId;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;
            self.common.busName = self.basicWorkOrderData.busName;
            self.common.auditResult = self.basicWorkOrderData.auditResult;
            self.common.isUploadReport = self.basicWorkOrderData.isUploadReport;
            self.common.remainAcceptTime =
              self.basicWorkOrderData.remainAcceptTime;
            self.common.alarmClearTime = self.basicWorkOrderData.alarmClearTime;
            self.common.mecNodeName = self.basicWorkOrderData.mecNodeName;
            self.common.emergencyLevel = self.basicWorkOrderData.emergencyLevel;
            this.ppResult = self.basicWorkOrderData.ppResultOri;
            this.analysisStatus = self.basicWorkOrderData.analysisStatus;
            this.showClxq = false;
            this.showLcrz = false;
            this.showLct = false;
            this.$nextTick(() => {
              this.showClxq = true;
              this.showLcrz = true;
              this.showLct = true;
            });
            if (
              self.basicWorkOrderData.auditResult == "0" ||
              self.basicWorkOrderData.feedbackResult == "0" ||
              self.headInfo.sheetStatus == "待定性审核" ||
              self.headInfo.sheetStatus == "待定性" ||
              self.headInfo.sheetStatus == "待归档" ||
              self.headInfo.sheetStatus == "已归档" ||
              self.headInfo.sheetStatus == "作废" ||
              self.headInfo.sheetStatus == "故障报告审核" ||
              self.headInfo.sheetStatus == "上传故障报告"
            ) {
              this.showFkdxq = false;
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatus == "异常归档" ||
        basicData.sheetStatus == "已归档" ||
        basicData.sheetStatus == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name, desc) {
      this.common.actionName = name;
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        case "追单":
          this.common.isObject = desc;
          this.dialogAfterSingleVisible = true;
          break;
        case "撤单":
          this.dialogRevokeVisible = true;
          break;
        case "已阅":
          this.haveRead();
          break;
        case "返单":
          //2023-10-19 需求变更（邮件）：去除集团通用流程现有“主告警清除，才可进行返单操作”的校验逻辑。
          // if (this.isBackSingle) {
          this.dialogBackSingleVisible = true;
          // } else {
          //   this.$message.warning("告警未被清除，不可返单");
          // }

          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核":
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止":
          this.dialogAbendVisible = true;
          break;
        case "异常终止审核":
          this.dialogAbendAuditVisible = true;
          break;
        case "转派":
          this.dialogTurnToSendVisible = true;
          break;
        case "转办":
          this.dialogTurnToVisible = true;
          break;
        case "回转":
          this.rotary();
          break;
        case "故障报告转派":
          this.dialogReportTransferVisible = true;
          break;
        case "重新转派":
          this.dialogAgainTurnToSendVisible = true;
          break;
        case "一键催办IVR":
          this.dialogOneKeyIvrNotice = true;
          break;
        case "定性":
          this.dialogQualitativeVisible = true;
          break;
        case "定性审核":
          this.showDxsh = false;
          this.$nextTick(() => {
            this.showDxsh = true;
          });
          this.dialogQualitativeReviewVisible = true;
          break;
        case "上传故障报告":
          this.dialogReportUpVisible = true;
          break;
      }
    },
    // 回转
    rotary() {
      apiRotationSubmit({
        "processInstId": this.common.processInstId,
        "workItemId": this.common.workItemId,
        "woId": this.common.woId,
        "sheetNo": this.common.sheetNo
      }).then(res => {
        console.log(res);
        if(res.status == 0){
          this.$message.success('回转成功');
          this.closeAndTurnAround();
        }else{
          this.$message.error(res.msg);
        }
        // this.closeAndTurnAround();
      }).catch(error => {
        this.$message.error('回转失败');
        console.log(error);
      });

    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInsId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            //根据新生成的workItemId再次调用显示按钮的接口
            this.getShowButton();
            this.getWorkOrderInfo();
            this.JumpDetails_Common(this.common);
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
      this.$refs.backSingleForm.onReset();
    },
    //返单提交
    dialogBackSingleSubmitClose(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogBackSingleVisible = false;
      if (data.currentPage) {
        this.JumpDetails_Common(this.common);
      } else {
        this.closeAndTurnAround();
      }
    },
    //定性提交
    qualitativeSubmit(data) {
      this.dialogQualitativeVisible = false;
      this.closeAndTurnAround();
    },
    qualitativeClose() {
      this.dialogQualitativeVisible = false;
      this.$refs.qualitative.onReset();
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
      this.$refs.qualitativeReview.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.dialogQualitativeReviewVisible = false;
      this.closeAndTurnAround();
    },
    //故障报告审核通过
    closeDialogReportAudit() {
      this.closeAndTurnAround();
    },
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    stageBackDialogCommitClose() {
      this.showClxq = false;
      this.showLcrz = false;
      this.$nextTick(() => {
        this.showLcrz = true;
        this.showClxq = true;
      });
      this.dialogStageFeedbackVisible = false;
    },
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.revokeSubmitLoading = true;
          let param = {
            processNode: this.common.processNode,
            sheetNo: this.common.sheetNo,
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("撤单成功");
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error("撤单失败");
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("撤单失败");
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //挂起关闭
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //挂起审核关闭
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    //解挂审核关闭
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.closeAndTurnAround();
    },
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交
    dialogAbendSubmitClose() {
      this.dialogAbendVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //追单
    dialogAfterSingleClose() {
      this.dialogAfterSingleVisible = false;
    },
    //追单
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //转派
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转办
    dialogTurnToClose() {
      this.dialogTurnToVisible = false;
    },
    //转派提交
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    //转派提交
    dialogTurnToSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToVisible = false;
      }
    },
    //重新转派
    dialogAgainTurnToSendClose() {
      this.dialogAgainTurnToSendVisible = false;
    },
    //故障报报转派
    dialogReportTransferClose(val) {
      if (val == "1") {
        this.dialogReportTransferVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogReportTransferVisible = false;
      }
    },
    dialogAgainTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogAgainTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogAgainTurnToSendVisible = false;
      }
    },
    getStatusStyle() {
      if (
        this.common.sheetStatus == "待定性" ||
        this.common.sheetStatus == "待定性审核"
      ) {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    alarmClearCallBack(data) {
      this.isBackSingle = data;
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
      });
    },
    currentProcessor() {
      this.dialogCurrentProcessorVisible = true;
    },
    currentProcessorClose() {
      this.dialogCurrentProcessorVisible = false;
    },
    JumpDetails_Common(data) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      this.$router.replace({
        name: "common_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
        },
      });
    },
    getReportRecords() {
      apiGetReportUpLog({
        woId: this.common.woId,
      })
        .then(res => {
          if (res.status == 0) {
            this.reportRecordsData = res?.data ?? [];
            // this.reportRecordsData = [{ auditStatus: "成功" }];
            if (this.reportRecordsData.length > 0) {
              this.reportRecordsData.forEach(el => {
                el.appendix = JSON.parse(el.appendix);
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.appendix = JSON.parse(tl.appendix);
                  });
                }
              });
              let isContains = this.headMoreDrops.some(
                item => item.command == "gzbgscjl"
              );
              if (!isContains) {
                this.headMoreDrops.unshift({
                  command: "gzbgscjl",
                  title: "故障报告上传记录",
                });
              }
              this.reportRecordsShow = true;
            } else {
              this.reportRecordsShow = false;
              this.headMoreDrops = this.headMoreDrops.filter(
                obj => obj.command != "gzbgscjl"
              );
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 智能体相关方法
    /**
     * 检查智能体可用性
     * 仅在核心网专业时启用
     */
    checkAiAgentAvailability() {
      // 判断是否为核心网专业
      if (!this.isCoreNetworkProfession()) {
        this.isAiAgentAvailable = false;
        return;
      }

      // 设置智能体功能可用
      this.isAiAgentAvailable = true;

      // 调用检查智能体存在性的接口（可选）
      // this.checkAiAgentExists();
    },

    /**
     * 判断是否为核心网专业
     */
    isCoreNetworkProfession() {
      // 临时开启所有专业类型的智能体功能，便于测试
      // TODO: 正式环境中应该只在核心网专业启用
      return true;

      // 原有的核心网专业判断逻辑（已注释）
      // // 通过专业类型名称判断
      // console.log("this.basicWorkOrderData?.professionalType", this.basicWorkOrderData?.professionalType);
      // const professionalTypeName = this.basicWorkOrderData?.professionalType || this.common?.professionalTypeName;
      // if (professionalTypeName === '核心网') {
      //   return true;
      // }

      // // 通过专业类型ID判断（核心网专业ID为31）
      // const professionalTypeId = this.basicWorkOrderData?.professionalTypeId || this.common?.professionalType;
      // if (professionalTypeId === '31' || professionalTypeId === 31) {
      //   return true;
      // }

      // return false;
    },

    /**
     * 检查智能体是否存在
     * TODO: 接口路径和参数需要根据实际情况调整
     */
    async checkAiAgentExists() {
      try {
        this.aiAgentLoading = true;
        this.aiAgentError = false;

        // 调用智能体检查接口
        const response = await checkAiAgentExists({
          woId: this.common.woId,
          professionalType: this.common.professionalType,
          professionalTypeName: this.common.professionalTypeName
        });

        if (response && response.exists) {
          this.aiAgentConfig = response.config;
          this.buildAiAgentUrl();
          this.showAiAgent = true;
        }
      } catch (error) {
        console.error('检查智能体失败:', error);
        this.aiAgentError = true;
      } finally {
        this.aiAgentLoading = false;
      }
    },



    /**
     * 构建智能体页面URL
     */
    buildAiAgentUrl() {
      if (!this.aiAgentConfig) {
        return;
      }

      const baseUrl = this.aiAgentConfig.baseUrl;
      const params = new URLSearchParams({
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        sheetNo: this.common.sheetNo,
        professionalType: this.common.professionalType,
        token: this.token,
        globalUniqueID: sessionStorage.getItem('globalUniqueID') || ''
      });

      this.aiAgentUrl = `${baseUrl}?${params.toString()}`;
    },

    /**
     * 智能体iframe加载完成
     */
    onAiAgentLoad() {
      this.aiAgentLoading = false;
      this.aiAgentError = false;

      // 设置iframe通信
      this.setupAiAgentCommunication();
    },

    /**
     * 智能体iframe加载错误
     */
    onAiAgentError() {
      this.aiAgentLoading = false;
      this.aiAgentError = true;
    },

    /**
     * 重试加载智能体
     */
    retryLoadAiAgent() {
      this.aiAgentError = false;
      this.aiAgentLoading = true;
      this.buildAiAgentUrl();

      // 重新加载iframe
      this.$nextTick(() => {
        if (this.$refs.aiAgentIframe) {
          this.$refs.aiAgentIframe.src = this.aiAgentUrl;
        }
      });
    },

    /**
     * 切换智能体显示状态
     */
    toggleAiAgent() {
      this.showAiAgent = !this.showAiAgent;

      // 如果是首次显示且没有URL，可以尝试加载
      if (this.showAiAgent && !this.aiAgentUrl && !this.aiAgentLoading) {
        // 这里可以调用实际的智能体检查接口
        // this.checkAiAgentExists();
      }
    },

    /**
     * 关闭智能体
     */
    closeAiAgent() {
      this.showAiAgent = false;
      // 保留URL和配置，便于重新打开
      // this.aiAgentUrl = '';
      // this.aiAgentConfig = null;
      this.aiAgentError = false;
      this.aiAgentLoading = false;
    },

    /**
     * 设置智能体通信
     */
    setupAiAgentCommunication() {
      // 监听来自智能体iframe的消息
      window.addEventListener('message', this.handleAiAgentMessage, false);
    },

    /**
     * 处理智能体消息
     */
    handleAiAgentMessage(event) {
      // 验证消息来源
      if (!this.aiAgentUrl || !event.origin) {
        return;
      }

      try {
        const message = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

        switch (message.type) {
          case 'ai-agent-ready':
            // 智能体准备就绪，发送工单信息
            this.sendWorkOrderInfoToAiAgent();
            break;
          case 'ai-agent-action':
            // 智能体请求执行操作
            this.handleAiAgentAction(message.payload);
            break;
          case 'ai-agent-resize':
            // 智能体请求调整大小
            this.handleAiAgentResize(message.payload);
            break;
          default:
            console.log('未知的智能体消息类型:', message.type);
        }
      } catch (error) {
        console.error('处理智能体消息失败:', error);
      }
    },

    /**
     * 向智能体发送工单信息
     */
    sendWorkOrderInfoToAiAgent() {
      if (!this.$refs.aiAgentIframe) {
        return;
      }

      const workOrderInfo = {
        type: 'work-order-info',
        payload: {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          sheetNo: this.common.sheetNo,
          sheetStatus: this.common.sheetStatus,
          professionalType: this.common.professionalType,
          basicInfo: this.basicWorkOrderData,
          headInfo: this.headInfo
        }
      };

      this.$refs.aiAgentIframe.contentWindow.postMessage(
        JSON.stringify(workOrderInfo),
        '*'
      );
    },

    /**
     * 处理智能体操作请求
     */
    handleAiAgentAction(payload) {
      // TODO: 根据智能体的操作请求执行相应操作
      console.log('智能体操作请求:', payload);

      // 示例：智能体请求跳转到某个tab
      if (payload.action === 'navigate-to-tab') {
        this.onHeadHandleClick(payload.tabName);
      }
    },

    /**
     * 处理智能体大小调整请求
     */
    handleAiAgentResize(payload) {
      // TODO: 根据需要调整智能体区域大小
      console.log('智能体大小调整请求:', payload);
    }
  },
  beforeDestroy() {
    // 清理智能体通信监听器
    window.removeEventListener('message', this.handleAiAgentMessage, false);
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
  }

  .head-handle-wrap {
    text-align: right;

    .more-dropdown {
      padding: 0;

      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }

    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }

  .divider {
    margin: 12px 0 16px;
  }

  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }

  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }

  .head-up-down {
    text-align: center;

    &>div:first-child {
      line-height: 20px;

      @include themify() {
        color: themed("$--color-text-regular");
      }
    }

    &>div:last-child {
      font-weight: 400;
      font-size: 18px;
      line-height: 28px;
      white-space: nowrap;

      &.text-primary {
        color: #c43c43;
      }
    }
  }

  .card-title {
    font-weight: 400;
    font-size: 15px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }

  // 页面级智能体布局样式
  .page-ai-agent-layout {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
    width: 100%;
    position: relative;
    transition: all 0.3s ease-in-out;

    .main-page-area {
      flex: 1;
      transition: all 0.3s ease-in-out;
      overflow-y: auto;
      min-height: 100vh;

      &.with-ai-agent {
        flex: 0 0 66.67%; // 2/3 宽度
        margin-right: 0;
      }
    }

    .page-ai-agent-area {
      flex: 0 0 33.33%; // 1/3 宽度
      min-width: 400px;
      max-width: 600px;
      display: flex;
      flex-direction: column;
      border-left: 1px solid #e4e7ed;
      background-color: #fff;
      transition: all 0.3s ease-in-out;
      position: fixed;
      right: 0;
      top: 0;
      height: 100vh;
      z-index: 999;

      .ai-agent-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #e4e7ed;
        background-color: #f5f7fa;
        flex-shrink: 0;

        .ai-agent-title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }

        .ai-agent-close-btn {
          padding: 4px;
          color: #909399;

          &:hover {
            color: #409eff;
          }
        }
      }

      .ai-agent-content {
        flex: 1;
        position: relative;
        overflow: hidden;

        iframe {
          border: none;
          width: 100%;
          height: 100%;
        }

        .ai-agent-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;
          text-align: center;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #f56c6c;
          }

          p {
            margin: 0 0 16px 0;
            font-size: 14px;
          }
        }

        .ai-agent-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          padding: 20px;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

          .placeholder-content {
            text-align: center;
            color: #606266;

            i {
              font-size: 64px;
              color: #409eff;
              margin-bottom: 20px;
              display: block;
            }

            h3 {
              margin: 0 0 16px 0;
              font-size: 18px;
              font-weight: 600;
              color: #303133;
            }

            p {
              margin: 0 0 8px 0;
              font-size: 14px;
              color: #606266;
              line-height: 1.5;
            }

            .placeholder-features {
              margin-top: 24px;
              display: flex;
              flex-direction: column;
              gap: 12px;

              .feature-item {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                padding: 8px 16px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 6px;
                font-size: 13px;
                color: #606266;

                i {
                  font-size: 16px;
                  color: #409eff;
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }

    // 响应式设计
    @media (max-width: 1200px) {
      .page-ai-agent-area {
        min-width: 350px;
        flex: 0 0 40%;
      }

      .main-page-area.with-ai-agent {
        flex: 0 0 60%;
      }
    }

    @media (max-width: 992px) {
      .page-ai-agent-area {
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 100vh;
        z-index: 1000;
        min-width: auto;
        max-width: none;
        border-left: none;
        border-top: 1px solid #e4e7ed;
      }

      .main-page-area.with-ai-agent {
        flex: 1;
        margin-right: 0;
      }
    }

    @media (max-width: 768px) {
      .page-ai-agent-area {
        .ai-agent-header {
          padding: 10px 12px;

          .ai-agent-title {
            font-size: 13px;
          }
        }
      }
    }
  }

  // 加载状态样式
  .ai-agent-content .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }

  // 智能体悬浮按钮样式
  .ai-agent-float-btn {
    position: fixed;
    right: 30px;
    bottom: 80px;
    width: 120px;
    height: 50px;
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
    color: white;
    font-size: 12px;
    font-weight: 500;
    user-select: none;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.5);
    }

    &.active {
      background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
      box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);

      &:hover {
        box-shadow: 0 6px 16px rgba(245, 108, 108, 0.5);
      }
    }

    i {
      font-size: 18px;
      margin-right: 6px;
      transition: transform 0.3s ease;
    }

    .btn-text {
      font-size: 12px;
      white-space: nowrap;
      transition: opacity 0.3s ease;
    }

    .btn-indicator {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      transition: all 0.3s ease;

      &.show {
        background-color: #67c23a;
        box-shadow: 0 0 6px rgba(103, 194, 58, 0.6);
      }

      &.hide {
        background-color: #909399;
        box-shadow: 0 0 6px rgba(144, 147, 153, 0.6);
      }
    }

    // 响应式适配
    @media (max-width: 768px) {
      right: 20px;
      bottom: 60px;
      width: 100px;
      height: 45px;
      font-size: 11px;

      i {
        font-size: 16px;
        margin-right: 4px;
      }

      .btn-text {
        font-size: 11px;
      }
    }

    // 动画效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}
</style>
