<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .button:hover {
            background: #66b1ff;
        }
        
        .button.secondary {
            background: #909399;
        }
        
        .button.secondary:hover {
            background: #a6a9ad;
        }
        
        .message-area {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            min-height: 100px;
            margin-top: 15px;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .message.sent {
            background: #e1f3d8;
            text-align: right;
        }
        
        .message.received {
            background: #fdf6ec;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            display: inline-block;
            margin-left: 10px;
        }
        
        .status.ready {
            background: #f0f9ff;
            color: #1890ff;
        }
        
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能体通信测试页面</h1>
            <p>此页面用于测试与父页面的postMessage通信</p>
            <div id="status">
                <span>状态: </span>
                <span class="status" id="statusText">初始化中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>发送消息到父页面</h3>
            <button class="button" onclick="sendReadyMessage()">发送就绪消息</button>
            <button class="button" onclick="sendActionMessage()">发送操作请求</button>
            <button class="button" onclick="sendResizeMessage()">发送调整大小请求</button>
            <button class="button secondary" onclick="sendCustomMessage()">发送自定义消息</button>
        </div>
        
        <div class="test-section">
            <h3>接收到的消息</h3>
            <div class="message-area" id="messageArea">
                <p>等待接收消息...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>工单信息</h3>
            <div class="message-area" id="workOrderInfo">
                <p>等待接收工单信息...</p>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        
        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            console.log('收到消息:', event.data);
            
            try {
                const message = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
                displayReceivedMessage(message);
                
                if (message.type === 'work-order-info') {
                    displayWorkOrderInfo(message.payload);
                }
            } catch (error) {
                console.error('解析消息失败:', error);
                displayReceivedMessage({ type: 'error', data: '消息解析失败: ' + error.message });
            }
        });
        
        // 页面加载完成后发送就绪消息
        window.addEventListener('load', function() {
            updateStatus('ready', '已就绪');
            setTimeout(() => {
                sendReadyMessage();
            }, 1000);
        });
        
        function updateStatus(type, text) {
            const statusElement = document.getElementById('statusText');
            statusElement.className = 'status ' + type;
            statusElement.textContent = text;
        }
        
        function sendReadyMessage() {
            const message = {
                type: 'ai-agent-ready',
                timestamp: new Date().toISOString()
            };
            sendMessageToParent(message);
        }
        
        function sendActionMessage() {
            const message = {
                type: 'ai-agent-action',
                payload: {
                    action: 'navigate-to-tab',
                    tabName: 'gjxq',
                    description: '请求跳转到告警详情页面'
                }
            };
            sendMessageToParent(message);
        }
        
        function sendResizeMessage() {
            const message = {
                type: 'ai-agent-resize',
                payload: {
                    width: '50%',
                    height: '400px',
                    description: '请求调整智能体区域大小'
                }
            };
            sendMessageToParent(message);
        }
        
        function sendCustomMessage() {
            const customData = prompt('请输入自定义消息内容:');
            if (customData) {
                const message = {
                    type: 'custom-message',
                    payload: {
                        data: customData,
                        timestamp: new Date().toISOString()
                    }
                };
                sendMessageToParent(message);
            }
        }
        
        function sendMessageToParent(message) {
            try {
                window.parent.postMessage(JSON.stringify(message), '*');
                displaySentMessage(message);
                console.log('发送消息:', message);
            } catch (error) {
                console.error('发送消息失败:', error);
                updateStatus('error', '发送失败');
            }
        }
        
        function displaySentMessage(message) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message sent';
            messageDiv.innerHTML = `
                <strong>发送:</strong> ${message.type}<br>
                <small>${new Date().toLocaleTimeString()}</small><br>
                <pre>${JSON.stringify(message, null, 2)}</pre>
            `;
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
        }
        
        function displayReceivedMessage(message) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message received';
            messageDiv.innerHTML = `
                <strong>接收:</strong> ${message.type || '未知类型'}<br>
                <small>${new Date().toLocaleTimeString()}</small><br>
                <pre>${JSON.stringify(message, null, 2)}</pre>
            `;
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
        }
        
        function displayWorkOrderInfo(workOrderInfo) {
            const workOrderArea = document.getElementById('workOrderInfo');
            workOrderArea.innerHTML = `
                <h4>工单基本信息</h4>
                <p><strong>工单ID:</strong> ${workOrderInfo.woId || 'N/A'}</p>
                <p><strong>工作项ID:</strong> ${workOrderInfo.workItemId || 'N/A'}</p>
                <p><strong>工单编号:</strong> ${workOrderInfo.sheetNo || 'N/A'}</p>
                <p><strong>工单状态:</strong> ${workOrderInfo.sheetStatus || 'N/A'}</p>
                <p><strong>专业类型:</strong> ${workOrderInfo.professionalType || 'N/A'}</p>
                <h4>详细信息</h4>
                <pre>${JSON.stringify(workOrderInfo, null, 2)}</pre>
            `;
        }
    </script>
</body>
</html>
