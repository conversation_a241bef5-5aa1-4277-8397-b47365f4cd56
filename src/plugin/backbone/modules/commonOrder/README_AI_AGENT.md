# 核心网工单详情页面智能体嵌套功能

## 功能概述

在核心网工单详情页面中实现智能体页面嵌套功能，为核心网专业的工单提供智能化辅助处理能力。

## 功能特性

### 1. 专业限制
- 仅在核心网专业时启用此功能
- 通过专业类型名称（"核心网"）或专业类型ID（31）进行判断
- 不影响其他专业的页面布局和功能

### 2. 响应式布局
- **存在智能体时**：左侧内容区域占2/3宽度，右侧智能体区域占1/3宽度
- **不存在智能体时**：左侧内容区域保持100%宽度，隐藏智能体区域
- 支持不同屏幕尺寸的响应式适配
- 平滑的布局切换动画效果

### 3. 智能体功能
- 动态检查智能体存在性
- iframe方式嵌入智能体页面
- 支持加载状态和错误处理
- 可手动关闭智能体区域

### 4. 通信机制
- 基于postMessage的iframe通信
- 支持工单信息传递
- 支持智能体操作请求处理
- 预留扩展接口

## 技术实现

### 1. 核心组件结构

```vue
<div class="ai-agent-layout" :class="{ 'ai-agent-active': showAiAgent }">
  <!-- 主要内容区域 -->
  <div class="main-content-area" :class="{ 'with-ai-agent': showAiAgent }">
    <!-- 原有的工单详情组件 -->
  </div>

  <!-- 智能体区域 -->
  <div class="ai-agent-area" v-if="showAiAgent">
    <!-- 智能体头部 -->
    <div class="ai-agent-header">
      <span class="ai-agent-title">智能体助手</span>
      <el-button @click="closeAiAgent">关闭</el-button>
    </div>
    
    <!-- 智能体内容 -->
    <div class="ai-agent-content">
      <iframe :src="aiAgentUrl" @load="onAiAgentLoad" @error="onAiAgentError"></iframe>
    </div>
  </div>
</div>
```

### 2. 关键方法

#### 专业判断
```javascript
isCoreNetworkProfession() {
  const professionalTypeName = this.basicWorkOrderData?.professionalType;
  const professionalTypeId = this.basicWorkOrderData?.professionalTypeId;
  
  return professionalTypeName === '核心网' || 
         professionalTypeId === '31' || 
         professionalTypeId === 31;
}
```

#### 智能体检查
```javascript
async checkAiAgentExists() {
  const response = await checkAiAgentExists({
    woId: this.common.woId,
    professionalType: this.common.professionalType,
    professionalTypeName: this.common.professionalTypeName
  });
  
  if (response && response.exists) {
    this.showAiAgent = true;
    this.buildAiAgentUrl();
  }
}
```

#### 通信处理
```javascript
handleAiAgentMessage(event) {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'ai-agent-ready':
      this.sendWorkOrderInfoToAiAgent();
      break;
    case 'ai-agent-action':
      this.handleAiAgentAction(message.payload);
      break;
  }
}
```

### 3. API接口

#### 智能体检查接口
- **路径**: `/backbone/workflow/checkAiAgent`
- **方法**: POST
- **参数**:
  ```json
  {
    "woId": "工单ID",
    "professionalType": "专业类型ID",
    "professionalTypeName": "专业类型名称"
  }
  ```
- **返回**:
  ```json
  {
    "exists": true,
    "config": {
      "baseUrl": "智能体页面基础URL",
      "features": ["chat", "analysis"]
    }
  }
  ```

### 4. 样式特性

#### 响应式断点
- **大屏幕（>1200px）**: 主内容2/3，智能体1/3
- **中等屏幕（992px-1200px）**: 主内容60%，智能体40%
- **小屏幕（<992px）**: 垂直布局，智能体固定高度300px

#### 动画效果
- 布局切换：0.3s ease-in-out过渡
- 加载状态：半透明遮罩
- 错误状态：友好的错误提示界面

## 使用说明

### 1. 启用条件
- 工单必须属于核心网专业
- 智能体检查接口返回存在状态
- 页面加载完成后自动检查

### 2. 用户交互
- 智能体区域可通过右上角关闭按钮手动关闭
- 加载失败时提供重试按钮
- 支持智能体与主页面的交互操作

### 3. 开发配置
- 智能体URL通过接口配置返回
- 支持传递工单相关参数
- 可扩展通信协议

## 扩展说明

### 1. 其他专业适配
如需在其他专业中启用智能体功能，修改 `isCoreNetworkProfession()` 方法：

```javascript
isCoreNetworkProfession() {
  const professionalTypeName = this.basicWorkOrderData?.professionalType;
  
  // 添加其他专业类型
  const supportedProfessions = ['核心网', 'IP专业', '平台'];
  return supportedProfessions.includes(professionalTypeName);
}
```

### 2. 通信协议扩展
在 `handleAiAgentMessage()` 方法中添加新的消息类型处理：

```javascript
case 'new-message-type':
  this.handleNewMessageType(message.payload);
  break;
```

### 3. 接口参数扩展
根据实际需求在 `checkAiAgentExists()` 中添加更多参数：

```javascript
const response = await checkAiAgentExists({
  woId: this.common.woId,
  professionalType: this.common.professionalType,
  // 添加新参数
  sheetStatus: this.common.sheetStatus,
  emergencyLevel: this.common.emergencyLevel
});
```

## 注意事项

1. **性能考虑**: 智能体页面加载不应影响主页面性能
2. **错误处理**: 需要完善的错误处理和用户提示
3. **安全性**: iframe通信需要验证消息来源
4. **兼容性**: 确保在不同浏览器中正常工作
5. **测试**: 需要在生产和测试环境中验证功能

## 后续开发

1. 完善智能体检查接口的实际实现
2. 根据业务需求扩展通信协议
3. 优化响应式布局在更多设备上的表现
4. 添加更多的用户交互功能
5. 完善错误处理和日志记录
